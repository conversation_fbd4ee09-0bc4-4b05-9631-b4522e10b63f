import { Injectable, Logger } from "@nestjs/common";
import { Cron, CronExpression } from "@nestjs/schedule";
import { TeamEvent, isError, CustomDate, Email, PlayerId } from "@mio/helpers";

import { TeamEventRepository } from "../team-events/instances/team-event.repository";
import { EmailService } from "../email/email.service";
import { PlayerRepository } from "../player/player.repository";

@Injectable()
export class RemindersService {
  private readonly logger = new Logger(RemindersService.name);

  constructor(
    private teamEventRepository: TeamEventRepository,
    private emailService: EmailService,
    private playerRepository: PlayerRepository,
  ) {}

  @Cron(CronExpression.EVERY_HOUR)
  async handleReminders() {
    this.logger.log("Running hourly event reminder check...");

    const events = await this.teamEventRepository.findEventsForReminder();

    if (isError(events)) {
      this.logger.error("Error fetching events for reminder", events);
      return;
    }

    if (events.length === 0) {
      this.logger.log("No events found requiring reminders.");
      return;
    }

    for (const event of events) {
      const invitedPlayerIds =
        event.type === TeamEvent.TeamEventType.Single
          ? event.invitations
          : event.recurringEvent.invitations;

      const respondentPlayerIds = (event.respondents || []).map((r) => r.player);

      const nonRespondingPlayerIds = invitedPlayerIds.filter(
        (id) => !respondentPlayerIds.includes(id),
      );

      if (nonRespondingPlayerIds.length === 0) {
        continue;
      }

      const playersToRemind: PlayerId[] = [];

      for (const playerId of nonRespondingPlayerIds) {
        const reminderSent = (event.remindersSent || []).some((r) => r.playerId === playerId);

        // for now we're only sending 1 reminder email per event per player
        if (!reminderSent) {
          playersToRemind.push(playerId);
        }
      }

      if (playersToRemind.length === 0) {
        continue;
      }

      const players = await this.playerRepository.getByIds(playersToRemind);

      if (isError(players)) {
        this.logger.error("Error fetching players for reminder", players);
        continue;
      }

      const playerEmails = players.map((p) => p.email).filter((e): e is Email => !!e);

      if (playerEmails.length === 0) {
        continue;
      }

      await this.emailService.sendEventReminder(playerEmails, event);

      const now = CustomDate.now();
      const updatedRemindersSent = [...(event.remindersSent || [])];

      for (const playerId of playersToRemind) {
        const existingReminderIndex = updatedRemindersSent.findIndex(
          (r) => r.playerId === playerId,
        );

        if (existingReminderIndex > -1) {
          updatedRemindersSent[existingReminderIndex] = { playerId, sentAt: now };
        } else {
          updatedRemindersSent.push({ playerId, sentAt: now });
        }
      }

      const updatedEvent: TeamEvent.TeamEvent = {
        ...event,
        remindersSent: updatedRemindersSent,
      };

      const updateResult = await this.teamEventRepository.updateEvent(updatedEvent);

      if (isError(updateResult)) {
        this.logger.error(
          `Failed to update event ${event.id} with reminder timestamps`,
          updateResult,
        );
      } else {
        this.logger.log(`Sent reminders for event ${event.id} to ${playerEmails.length} players.`);
      }
    }
  }
}
